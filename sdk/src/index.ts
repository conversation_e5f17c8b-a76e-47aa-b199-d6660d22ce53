// Re-exports
export { FlakinessReport } from '@flakiness/flakiness-report';
export { createEnvironment } from './createEnvironment.js';
export { createTestStepSnippetsInplace } from './createTestStepSnippets.js';
export { FlakinessProjectConfig } from './flakinessProjectConfig.js';
export { computeGitRoot, gitCommitInfo } from './git.js';
export * as pathutils from './pathutils.js';
export { createDataAttachment, createFileAttachment, ExternalAttachment, ReportUploader } from './reportUploader.js';
export { ReportUtils } from './reportUtils.js';
export { saveReport } from './saveReport.js';
export { showReport } from './showReport.js';
export { SystemUtilizationSampler } from './systemUtilizationSampler.js';
export { stripAnsi } from './utils.js';

// This function tries to search for well-known env variables to figure out run URL.
export function inferRunUrl() {
  if (process.env.GITHUB_REPOSITORY && process.env.GITHUB_RUN_ID)
    return `https://github.com/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}`;
  return undefined;
}
